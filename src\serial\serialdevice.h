#ifndef SERIALDEVICE_H
#define SERIALDEVICE_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QSerialPort>
#include <QTimer>

/**
 * @brief 串口设备接口
 */
class ISerialDevice
{
public:
    virtual ~ISerialDevice() = default;

    /**
     * @brief 打开串口
     * @return 成功返回true
     */
    virtual bool open() = 0;

    /**
     * @brief 关闭串口
     */
    virtual void close() = 0;

    /**
     * @brief 检查串口是否打开
     * @return 打开返回true
     */
    virtual bool isOpen() const = 0;

    /**
     * @brief 写入数据
     * @param data 要写入的数据
     * @return 实际写入的字节数
     */
    virtual qint64 write(const QByteArray& data) = 0;

    /**
     * @brief 读取所有可用数据
     * @return 读取到的数据
     */
    virtual QByteArray readAll() = 0;

    /**
     * @brief 获取串口名称
     * @return 串口名称
     */
    virtual QString portName() const = 0;

    /**
     * @brief 设置串口参数
     * @param portName 串口名称
     * @param baudRate 波特率
     * @param dataBits 数据位
     * @param parity 校验位
     * @param stopBits 停止位
     * @param flowControl 流控制
     * @return 设置成功返回true
     */
    virtual bool setPortSettings(const QString& portName,
                                QSerialPort::BaudRate baudRate,
                                QSerialPort::DataBits dataBits,
                                QSerialPort::Parity parity,
                                QSerialPort::StopBits stopBits,
                                QSerialPort::FlowControl flowControl) = 0;
};

/**
 * @brief 串口设备实现类
 */
class SerialDevice : public QObject, public ISerialDevice
{
    Q_OBJECT

public:
    explicit SerialDevice(QObject *parent = nullptr);
    ~SerialDevice();

    // 实现ISerialDevice接口
    bool open() override;
    void close() override;
    bool isOpen() const override;
    qint64 write(const QByteArray& data) override;
    QByteArray readAll() override;
    QString portName() const override;
    bool setPortSettings(const QString& portName,
                        QSerialPort::BaudRate baudRate,
                        QSerialPort::DataBits dataBits,
                        QSerialPort::Parity parity,
                        QSerialPort::StopBits stopBits,
                        QSerialPort::FlowControl flowControl) override;

    /**
     * @brief 设置读写超时时间
     * @param readTimeout 读超时(毫秒)
     * @param writeTimeout 写超时(毫秒)
     */
    void setTimeout(int readTimeout, int writeTimeout);

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    QString lastError() const;

    /**
     * @brief 清空输入输出缓冲区
     */
    void clear();

    /**
     * @brief 获取可用的串口列表
     * @return 串口名称列表
     */
    static QStringList availablePorts();

signals:
    /**
     * @brief 数据接收信号
     * @param data 接收到的数据
     */
    void dataReceived(const QByteArray& data);

    /**
     * @brief 错误信号
     * @param error 错误信息
     */
    void errorOccurred(const QString& error);

    /**
     * @brief 连接状态变化信号
     * @param connected 是否连接
     */
    void connectionChanged(bool connected);

private slots:
    /**
     * @brief 处理串口数据接收
     */
    void onDataReceived();

    /**
     * @brief 处理串口错误
     * @param error 串口错误
     */
    void onSerialError(QSerialPort::SerialPortError error);

    /**
     * @brief 处理写超时
     */
    void onWriteTimeout();

private:
    QSerialPort* m_serialPort;          // 串口对象
    QTimer* m_writeTimer;               // 写超时定时器
    int m_readTimeout;                  // 读超时时间
    int m_writeTimeout;                 // 写超时时间
    QString m_lastError;                // 最后的错误信息

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setLastError(const QString& error);
};

#endif // SERIALDEVICE_H
