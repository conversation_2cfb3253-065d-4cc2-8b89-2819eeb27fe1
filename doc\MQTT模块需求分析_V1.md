# MQTT模块需求分析_V1

| **版本** | **日期**  | **编者** | **说明** |
| -------- | --------- | -------- | -------- |
| V1.0     | 2025/8/15 |          | 初始版本 |

## 1. 概述

本文档基于《MCU与MQTT模块交互协议说明_V1》，重新梳理MQTT模块的功能需求。

### 1.1 程序定位
本程序是一个 **MQTT模块固件**，模拟类似EC20等模块的行为：
- **MCU（Master）** 通过串口发送AT指令
- **MQTT模块（Slave）** 接收并执行AT指令，返回响应

### 1.2 核心原则
- **指令驱动**：所有操作由AT指令触发，无自主行为
- **参数透传**：msgid等参数完全来自AT指令，不自行管理
- **配置动态**：不使用配置文件，所有配置来自AT指令
- **多客户端**：支持0-5共6个独立的MQTT客户端实例

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────┐    串口AT指令    ┌─────────────────┐
│     MCU     │ ──────────────→ │   AT指令解析器   │
│  (Master)   │ ←────────────── │                │
└─────────────┘   响应/URC      └─────────────────┘
                                        │
                                        ▼
                                ┌─────────────────┐
                                │ ClientManager   │
                                │   (0-5客户端)    │
                                └─────────────────┘
                                        │
                                        ▼
                                ┌─────────────────┐
                                │  mqttClient     │
                                │   实例 × 6      │
                                └─────────────────┘
```

### 2.2 数据流
```
串口输入 → AT指令解析 → 参数验证 → 立即响应(OK/ERROR)
                                    ↓
                              客户端操作执行
                                    ↓
                              异步URC生成 → 串口输出
```

## 3. 模块功能需求

### 3.1 AT指令解析器

#### 功能职责
- 从串口接收AT指令（以`<CR><LF>`结尾）
- 解析指令类型和参数
- 验证参数格式和范围
- 返回立即响应：`OK` 或 `ERROR`

#### 需要支持的AT指令
| 指令 | 功能 | 参数说明 |
|------|------|----------|
| `AT` | 基础测试 | 无参数 |
| `AT+QMTCFG=...` | 客户端配置 | 配置类型、客户端索引、配置值 |
| `AT+QMTOPEN=...` | 打开网络连接 | 客户端索引、服务器地址、端口 |
| `AT+QMTOPEN?` | 查询网络状态 | 无参数 |
| `AT+QMTCONN=...` | 建立MQTT连接 | 客户端索引、客户端ID、用户名、密码 |
| `AT+QMTCONN?` | 查询连接状态 | 无参数 |
| `AT+QMTSUB=...` | 订阅主题 | 客户端索引、消息ID、主题、QoS |
| `AT+QMTSUB=?` | 查询订阅参数支持 | 无参数 |
| `AT+QMTPUBEX=...` | 发布消息 | 客户端索引、消息ID、QoS、保留标志、主题、长度 |
| `AT+QMTDISC=...` | 断开MQTT连接 | 客户端索引 |
| `AT+QMTCLOSE=...` | 关闭网络连接 | 客户端索引 |

#### 错误处理
- **格式错误**：指令格式不正确时返回 `ERROR`
- **参数错误**：参数超出范围或类型错误时返回 `ERROR`
- **运行时错误**：通过URC上报，不影响指令响应

### 3.2 ClientManager（客户端管理器）

#### 功能职责
- **多客户端管理**：管理6个客户端实例（client_idx: 0-5）
- **状态维护**：维护每个客户端的网络状态、连接状态、配置信息
- **操作路由**：根据client_idx将操作路由到对应客户端
- **状态查询**：响应查询指令，返回客户端状态信息

#### 客户端状态管理
每个客户端需要维护以下状态：
- **网络状态**：未连接/已连接/连接中/断开中
- **MQTT连接状态**：初始化/连接中/已连接/断开中
- **连接信息**：服务器地址、端口、客户端ID
- **配置信息**：会话类型、遗嘱消息、超时设置、接收模式
- **订阅列表**：已订阅的主题和QoS

#### 不需要的功能
- ❌ 配置文件管理（所有配置来自AT指令）
- ❌ msgid管理（msgid由MCU在AT指令中提供）
- ❌ 自动重连（重连由MCU控制）

### 3.3 mqttClient（MQTT客户端）

#### 功能职责
- **连接管理**：TCP连接建立/关闭 + MQTT连接建立/断开
- **订阅管理**：维护订阅列表，处理订阅/取消订阅
- **消息处理**：发布消息、接收消息
- **事件上报**：通过URC向MCU上报异步事件

#### 生命周期管理
1. **初始化**：创建客户端实例
2. **配置**：接收并应用配置参数
3. **网络连接**：建立TCP连接
4. **MQTT连接**：建立MQTT协议连接
5. **业务操作**：订阅、发布、接收消息
6. **断开连接**：断开MQTT连接
7. **关闭网络**：关闭TCP连接

#### 关键设计要点
- **参数透传**：使用AT指令中提供的msgid，不自己生成
- **配置动态**：所有配置参数来自AT指令，不使用默认配置
- **异步操作**：操作完成后发送URC，不阻塞指令处理

### 3.4 URC事件生成器

#### 功能职责
- 生成异步URC响应（格式：`<CR><LF>+URC...<CR><LF>`）
- 通过串口发送给MCU

#### 需要支持的URC事件
| URC | 触发时机 | 格式 |
|-----|----------|------|
| `+QMTOPEN` | 网络连接完成 | `+QMTOPEN: <client_idx>,<result>` |
| `+QMTCONN` | MQTT连接完成 | `+QMTCONN: <client_idx>,<result>[,<ret_code>]` |
| `+QMTSUB` | 订阅完成 | `+QMTSUB: <client_idx>,<msgid>,<result>[,<value>]` |
| `+QMTPUBEX` | 发布完成 | `+QMTPUBEX: <client_idx>,<msgid>,<result>` |
| `+QMTDISC` | 断开完成 | `+QMTDISC: <client_idx>,<result>` |
| `+QMTCLOSE` | 关闭完成 | `+QMTCLOSE: <client_idx>,<result>` |
| `+QMTRECV` | 接收到消息 | `+QMTRECV: <client_idx>,<msgid>,"<topic>",<len>,"<payload>"` |
| `+QMTSTAT` | 连接状态变化 | `+QMTSTAT: <client_idx>,<err_code>` |
| `+QMTPING` | 心跳状态变化 | `+QMTPING: <client_idx>,<result>` |

## 4. 技术要求

### 4.1 串口通信
- **输入格式**：AT指令以`<CR><LF>`结尾
- **输出格式**：响应和URC都用`<CR><LF>`包围
- **字符编码**：ASCII
- **波特率**：可配置（默认115200）

### 4.2 MQTT协议
- **协议版本**：MQTT 3.1.1
- **QoS支持**：主要支持QoS 1（至少一次传输）
- **客户端数量**：最多6个并发客户端
- **消息ID范围**：1-65535（由MCU管理）

### 4.3 错误处理
- **指令错误**：格式错误返回`ERROR`
- **运行时错误**：通过`+QMTSTAT`等URC上报
- **网络错误**：自动检测并上报，不自动重连

### 4.4 性能要求
- **响应时间**：AT指令立即响应（<100ms）
- **并发支持**：支持6个客户端同时工作
- **内存管理**：合理管理连接和消息缓存

## 5. 开发优先级

### 阶段一：基础框架
1. AT指令解析器
2. ClientManager基础结构
3. 基础AT指令支持（AT、AT+QMTOPEN等）

### 阶段二：核心功能
1. MQTT连接管理
2. 消息发布功能
3. URC事件生成

### 阶段三：完整功能
1. 消息订阅和接收
2. 状态查询指令
3. 错误处理和状态上报

### 阶段四：优化完善
1. 性能优化
2. 错误处理完善
3. 测试和调试工具
