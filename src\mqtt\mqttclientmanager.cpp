#include "mqttclientmanager.h"
#include "log.h"

MqttClientManager::MqttClientManager(QObject *parent)
    : QObject(parent)
{

}

MqttClientManager::~MqttClientManager()
{
    logTrace("MqttClientManager::~MqttClientManager()");
    
    // 清理所有客户端实例
    for (auto it = m_clients.begin(); it != m_clients.end(); ++it) {
        delete it.value();
    }
    m_clients.clear();
}

// === AT指令处理接口实现 ===

bool MqttClientManager::handleQmtOpen(int clientIdx, const QString& hostname, int port)
{
    logInfo(QString("handleQmtOpen: %1 %2 %3").arg(clientIdx).arg(hostname).arg(port));

    // 检查客户端索引有效性
    if (!isValidClientIndex(clientIdx)) {
        logError(QString("Invalid client index: %1").arg(clientIdx));
        return false;
    }

    // 获取或创建客户端实例
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        client = createClient(clientIdx);
        if (!client) {
            logError(QString("Failed to create client: %1").arg(clientIdx));
            return false;
        }
    }

    return client->openNetwork(hostname, port);
}

bool MqttClientManager::handleQmtConn(int clientIdx, const QString& clientId, 
                                      const QString& username, const QString& password)
{
    logInfo(QString("handleQmtConn: %1 %2").arg(clientIdx).arg(clientId));
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        logError(QString("Invalid client index: %1").arg(clientIdx));
        return false;
    }
    
    return client->connectMqtt(clientId, username, password);
}

bool MqttClientManager::handleQmtSub(int clientIdx, int msgId, const QString& topic, int qos)
{
    logInfo(QString("handleQmtSub: %1 %2 %3 %4").arg(clientIdx).arg(msgId).arg(topic).arg(qos));
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        logError(QString("Invalid client index: %1").arg(clientIdx));
        return false;
    }
    
    return client->subscribe(msgId, topic, qos);
}

bool MqttClientManager::handleQmtPub(int clientIdx, int msgId, int qos, bool retain,
                                       const QString& topic, const QByteArray& payload)
{
    logInfo(QString("handleQmtPub: %1 %2 %3 %4").arg(clientIdx).arg(msgId).arg(topic).arg(qos));
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        logError(QString("Invalid client index: %1").arg(clientIdx));
        return false;
    }
    
    return client->publish(msgId, qos, retain, topic, payload);
}

bool MqttClientManager::handleQmtDisc(int clientIdx)
{
    logInfo(QString("handleQmtDisc: %1").arg(clientIdx));
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        logError(QString("Invalid client index: %1").arg(clientIdx));
        return false;
    }
    
    return client->disconnect();
}

bool MqttClientManager::handleQmtClose(int clientIdx)
{
    logInfo(QString("handleQmtClose: %1").arg(clientIdx));

    MqttClient* client = getClient(clientIdx);
    if (!client) {
        logError(QString("Client %1 does not exist").arg(clientIdx));
        return false;
    }

    bool result = client->closeNetwork();

    // 网络关闭后，销毁客户端实例以释放资源
    // 注意：这里不立即销毁，而是等待URC事件确认关闭完成后再销毁
    // 实际销毁将在onClientNetworkClosed槽函数中进行

    return result;
}

// === 状态查询接口实现 ===

QString MqttClientManager::handleQmtOpenQuery() const
{
    logInfo("handleQmtOpenQuery");
    
    QStringList results;
    
    for (int i = 0; i < MAX_CLIENTS; ++i) {
        MqttClient* client = getClient(i);
        if (client && client->isNetworkOpen()) {
            QString info = client->getNetworkInfo();
            if (!info.isEmpty()) {
                results << info;
            }
        }
    }
    
    return results.join("\r\n");
}

QString MqttClientManager::handleQmtConnQuery() const
{
    logInfo("handleQmtConnQuery");
    
    QStringList results;
    
    for (int i = 0; i < MAX_CLIENTS; ++i) {
        MqttClient* client = getClient(i);
        if (client) {
            int state = client->getConnectionState();
            if (state > 1) { // 只显示有连接活动的客户端
                results << QString("+QMTCONN: %1,%2").arg(i).arg(state);
            }
        }
    }
    
    return results.join("\r\n");
}

// === 内部辅助函数实现 ===

MqttClient* MqttClientManager::createClient(int clientIdx)
{
    if (!isValidClientIndex(clientIdx)) {
        logError(QString("Invalid client index for creation: %1").arg(clientIdx));
        return nullptr;
    }

    // 检查客户端是否已存在
    if (m_clients.contains(clientIdx)) {
        logInfo(QString("Client %1 already exists").arg(clientIdx));
        return m_clients.value(clientIdx);
    }

    // 创建新的客户端实例
    MqttClient* client = new MqttClient(clientIdx, this);
    if (!client) {
        logError(QString("Failed to allocate memory for client %1").arg(clientIdx));
        return nullptr;
    }

    // 连接信号
    connectClientSignals(client);

    // 添加到映射表
    m_clients.insert(clientIdx, client);

    logInfo(QString("Client %1 created on demand").arg(clientIdx));
    return client;
}

bool MqttClientManager::destroyClient(int clientIdx)
{
    if (!isValidClientIndex(clientIdx)) {
        logWarnning(QString("Invalid client index for destruction: %1").arg(clientIdx));
        return false;
    }

    MqttClient* client = m_clients.value(clientIdx, nullptr);
    if (!client) {
        logDebug(QString("Client %1 does not exist, nothing to destroy").arg(clientIdx));
        return false;
    }

    // 从映射表中移除
    m_clients.remove(clientIdx);

    // 删除客户端实例
    client->deleteLater();

    logDebug(QString("Client %1 destroyed").arg(clientIdx));
    return true;
}

MqttClient* MqttClientManager::getClient(int clientIdx) const
{
    if (!isValidClientIndex(clientIdx)) {
        return nullptr;
    }

    return m_clients.value(clientIdx, nullptr);
}

bool MqttClientManager::isValidClientIndex(int clientIdx) const
{
    return (clientIdx >= 0 && clientIdx < MAX_CLIENTS);
}

void MqttClientManager::connectClientSignals(MqttClient* client)
{
    if (!client) {
        return;
    }
    
    // 连接所有客户端信号到管理器信号
    connect(client, &MqttClient::networkOpened,
            this, &MqttClientManager::networkOpened);
    
    connect(client, &MqttClient::mqttConnected,
            this, &MqttClientManager::mqttConnected);
    
    connect(client, &MqttClient::subscribed,
            this, &MqttClientManager::subscribed);
    
    connect(client, &MqttClient::published,
            this, &MqttClientManager::published);
    
    connect(client, &MqttClient::mqttDisconnected,
            this, &MqttClientManager::mqttDisconnected);
    
    connect(client, &MqttClient::networkClosed,
            this, &MqttClientManager::networkClosed);
    
    connect(client, &MqttClient::messageReceived,
            this, &MqttClientManager::messageReceived);
    
    connect(client, &MqttClient::statusChanged,
            this, &MqttClientManager::statusChanged);
}
